import {Component, HostListener, OnInit, Renderer2} from '@angular/core'; // Импортируйте Renderer2
import {animate, state, style, transition, trigger} from "@angular/animations";

interface FaqItem {
  id: number;
  question: string;
  answer: string;
  isOpen: boolean;
}

@Component({
  selector: 'app-main-component',
  standalone: false,
  templateUrl: './main-component.html',
  styleUrl: './main-component.css',
  animations:
    [
      trigger('fadeInOut', [
        state('void', style({opacity: 0})),
        transition('void => *', [
          style({opacity: 0}),
          animate('200ms ease-in-out')
        ]),
        transition('* => void', [
          animate('200ms ease-in-out', style({opacity: 0}))
        ])
      ])
    ]
})
export class MainComponent implements OnInit {

  constructor(private renderer: Renderer2) {
  }

  options = {
    path: 'assets/Animation7.json', // Путь к вашему JSON-файлу
  };

  images = [
    'assets/img/first.png',
    'assets/img/second.png',
    'assets/img/third.jpg',
    'assets/img/fourth.png',
  ];

  images2 = [
    'assets/img/2first.png',
    'assets/img/2second.jpg',
    'assets/img/2third.jpg',
    'assets/img/2fourth.png',
  ];

  images3 = [
    'assets/img/31.png',
    'assets/img/32.jpeg',
    'assets/img/33.png',
    'assets/img/34.png',
  ];

  // FAQ Accordion data
  faqItems: FaqItem[] = [
    {
      id: 1,
      question: 'Сколько стоит?',
      answer: 'В зависимости от филиала и города. Цены начинаются от 25000, вы можете узнать точную информацию у менеджера вашего города',
      isOpen: false
    },
    {
      id: 2,
      question: 'Какой график? Что с расписанием занятий?',
      answer: 'Есть группы утренние и послеобеденные, зависит от смены в школе. В неделю проводится 2 занятия с преподавателем по 1,5 часа.',
      isOpen: false
    },
    {
      id: 3,
      question: 'Со скольки лет берём?',
      answer: 'ITeasy принимает на обучение подростков от 12 лет',
      isOpen: false
    },
    {
      id: 4,
      question: 'После курсов ребенок станет программистом/ 3d-художником?',
      answer: 'Нет, мы не выпускаем готовых специалистов. Мы даём ребенку крепкий фундамент и понимание основных специальностей.',
      isOpen: false
    },
    {
      id: 5,
      question: 'Как следить за успехами ребенка?',
      answer: 'Каждый ребёнок создает своё собственное портфолио и добавляет туда свои работы. Также преподаватель группы ежемесячно отправляет отчёт по каждому ребенку лично родителю.',
      isOpen: false
    },
    {
      id: 6,
      question: 'Есть ли скидки или льготы?',
      answer: 'Скидка предоставляется только в том случае, если в ITeasy обучается два и более ребёнка из одной семьи. Размер скидки составляет 10% от стоимости обучения. Так же есть рефельная программа, вы можете уточнить информацию у менеджера вашего города',
      isOpen: false
    }
  ];

  isModalOpen = false;
  selectedImage: string | null = null;
  isLoggedIn = false;
  username?: string;


  ngOnInit() { 
  }

  openModal(image: string) {
    this.selectedImage = image;
    this.isModalOpen = true;
    this.renderer.addClass(document.body, 'no-scroll'); // Отключить прокрутку
  }

  closeModal() {
    this.isModalOpen = false;
    this.selectedImage = null;
    this.renderer.removeClass(document.body, 'no-scroll'); // Включить прокрутку
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape' && this.isModalOpen) {
      this.closeModal();
    }
  }

  // Accordion functionality
  toggleAccordion(itemId: number) {
    this.faqItems = this.faqItems.map(item => ({
      ...item,
      isOpen: item.id === itemId ? !item.isOpen : item.isOpen
    }));
  }

  // TrackBy function for performance optimization
  trackByFaqId(index: number, item: FaqItem): number {
    return item.id;
  }

  protected readonly close = close;
}
