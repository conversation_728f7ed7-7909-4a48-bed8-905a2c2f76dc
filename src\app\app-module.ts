import { NgModule, provideBrowserGlobalErrorListeners } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import {provideAnimationsAsync} from '@angular/platform-browser/animations/async';
import { AppRoutingModule } from './app-routing-module';
import { App } from './app';
import { MainComponent } from './features/main/components/main-component/main-component';
import { StudioPageComponent } from './features/studio/components/studio-page/studio-page.component';
import {LottieComponent, provideCacheableAnimationLoader, provideLottieOptions} from 'ngx-lottie';
import player from 'lottie-web';


@NgModule({
  declarations: [
    App,
    MainComponent,
    StudioPageComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule, 
    LottieComponent,
  ],
  providers: [
    provideBrowserGlobalErrorListeners(),
    provideAnimationsAsync(),
    provideLottieOptions({ player: () => player }),
  ],
  bootstrap: [App]
})
export class AppModule { }
