@import url(https://db.onlinewebfonts.com/c/7bcd455b5591df3cb4292491087801fb?family=Como+W01+Heavy) layer(fonts);

@font-face {
  font-family: "Como W01 Heavy";
  src: url(https://db.onlinewebfonts.com/c/7bcd455b5591df3cb4292491087801fb?family=Como+W01+Heavy) format('woff2');
  font-display: swap;
  font-weight: 700 900;
  font-style: normal;
}

.main-title{
  font-family: "Como W01 Heavy", "Arial Black", sans-serif;
  font-weight: 900;
}

.main-line-height{
  line-height:50px;
}

@media (max-width: 640px) {
  .main-line-height{
    line-height:33px;
  }
}

html {
  scroll-behavior: smooth;
}

/* Accordion animations */
.accordion-item {
  border-bottom: 1px solid #e5e7eb;
}

.accordion-content {
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.accordion-content.hidden {
  max-height: 0;
  opacity: 0;
}

.accordion-content:not(.hidden) {
  max-height: 500px;
  opacity: 1;
}

/* Smooth icon rotation */
.accordion-item svg {
  transition: transform 0.2s ease-in-out;
}

/* Hover effects for accordion buttons */
.accordion-item button:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
